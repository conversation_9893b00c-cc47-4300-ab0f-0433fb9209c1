"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Logo from "@/components/ui/logo";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, DollarSign, Mail, Phone, Target, User } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

// Import shared constants
const countries = [
  { code: "IN", name: "India", phoneCode: "+91" },
  { code: "AE", name: "UAE", phoneCode: "+971" },
  { code: "US", name: "United States", phoneCode: "+1" },
  { code: "GB", name: "United Kingdom", phoneCode: "+44" },
  { code: "CA", name: "Canada", phoneCode: "+1" },
  { code: "AU", name: "Australia", phoneCode: "+61" },
  { code: "SG", name: "Singapore", phoneCode: "+65" },
];

const industryOptions = [
  { value: "agriculture", label: "Agriculture & Agribusiness" },
  { value: "automotive", label: "Automotive & Auto Components" },
  { value: "bfsi", label: "Banking, Financial Services & Insurance (BFSI)" },
  { value: "consumer-goods", label: "Consumer Goods (FMCG, Retail, E-Commerce)" },
  { value: "education", label: "Education & Training" },
  { value: "energy", label: "Energy (Conventional & Renewable)" },
  { value: "healthcare", label: "Healthcare, Pharma & Biotechnology" },
  { value: "hospitality", label: "Hospitality, Travel & Leisure" },
  { value: "it", label: "Information Technology & Software Services" },
  { value: "logistics", label: "Logistics, Supply Chain & Transportation" },
  { value: "manufacturing", label: "Manufacturing & Industrial Products" },
  { value: "media", label: "Media, Entertainment & VFX" },
  { value: "mining", label: "Mining & Natural Resources" },
  { value: "professional", label: "Professional & Business Services" },
  { value: "real-estate", label: "Real Estate & Infrastructure" },
  { value: "telecom", label: "Telecom & Communications" },
  { value: "textiles", label: "Textiles, Apparel & Fashion" },
  { value: "utilities", label: "Utilities & Public Services" },
];

const investmentStageOptions = [
  { value: "early-growth", label: "Early Growth Capital" },
  { value: "expansion", label: "Expansion Capital" },
  { value: "late-stage", label: "Late Stage / Pre-IPO" },
  { value: "turnaround", label: "Turnaround / Special Situations" },
  { value: "minority-growth", label: "Minority Growth Equity" },
  { value: "buyout", label: "Buyout / Acquisition" },
  { value: "mezzanine", label: "Mezzanine / Structured Finance" },
  { value: "debt", label: "Debt Financing" },
  { value: "strategic", label: "Strategic / Corporate Investment" },
  { value: "impact", label: "Impact / ESG Investment" },
];

const expectedReturnsOptions = [
  { value: "under-10", label: "<10%" },
  { value: "11-15", label: "11% - 15%" },
  { value: "16-20", label: "16% - 20%" },
  { value: "21-25", label: "21% - 25%" },
  { value: "custom", label: "Custom" },
];

export default function InvestorSignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phoneCountryCode: "+91",
    phone: "",

    // Investment Profile
    investorType: "",
    minInvestment: "",
    maxInvestment: "",
    riskTolerance: "",

    // Preferences
    preferredIndustries: [] as string[],
    preferredIndustriesOther: "",
    preferredStages: [] as string[],
    investmentHorizon: "",
    expectedReturns: "",
    expectedReturnsCustom: ""
  });

  const updateFormData = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const toggleArrayValue = (field: string, value: string) => {
    const currentArray = formData[field as keyof typeof formData] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFormData(field, newArray);
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / totalSteps) * 100;

  // Using shared constants for consistency

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/investor" size="sm" />
          <Badge className="bg-gray-100 text-black border-gray-300">
            Investor Registration
          </Badge>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex justify-center mb-6">
            <Logo size="lg" showText={false} />
          </div>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-black">Investor Registration</h1>
            <span className="text-gray-600">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={progress} className="h-2 mb-2" />
          <p className="text-gray-600 text-sm">Join our network of qualified investors</p>
        </motion.div>

        {/* Step Content */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="bg-white border-gray-200 shadow-lg">
            <CardHeader>
              <CardTitle className="text-black flex items-center">
                {currentStep === 1 && <><User className="w-5 h-5 mr-2 text-black" />Personal Information</>}
                {currentStep === 2 && <><DollarSign className="w-5 h-5 mr-2 text-black" />Set your investment goals and risk profile</>}
                {currentStep === 3 && <><Target className="w-5 h-5 mr-2 text-black" />Investment Preferences</>}
              </CardTitle>
              <CardDescription className="text-gray-600">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Define your investment capacity and risk profile"}
                {currentStep === 3 && "Set your investment preferences and criteria"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-gray-700">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-gray-700">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="Doe"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-gray-700">Email Address *</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData("email", e.target.value)}
                        className="pl-10 bg-white border-gray-300 text-black"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-gray-700">Phone Number *</Label>
                    <div className="flex gap-2">
                      <select
                        value={formData.phoneCountryCode}
                        onChange={(e) => updateFormData("phoneCountryCode", e.target.value)}
                        className="w-24 bg-white border border-gray-300 text-black rounded-md px-2 py-2"
                      >
                        {countries.map((country) => (
                          <option key={country.code} value={country.phoneCode}>
                            {country.phoneCode}
                          </option>
                        ))}
                      </select>
                      <div className="relative flex-1">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => updateFormData("phone", e.target.value)}
                          className="pl-10 bg-white border-gray-300 text-black"
                          placeholder="98765 43210"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Set your investment goals and risk profile */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="investorType" className="text-gray-700">Investor Type *</Label>
                    <select
                      id="investorType"
                      value={formData.investorType}
                      onChange={(e) => updateFormData("investorType", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Type</option>
                      <option value="individual">Individual Investor</option>
                      <option value="hni">High Net Worth Individual</option>
                      <option value="family_office">Family Office</option>
                      <option value="institutional">Institutional Investor</option>
                      <option value="vc_fund">VC Fund</option>
                      <option value="pe_fund">PE Fund</option>
                      <option value="others">Others</option>
                    </select>
                  </div>



                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="minInvestment" className="text-gray-700">Min Investment per Deal *</Label>
                      <select
                        id="minInvestment"
                        value={formData.minInvestment}
                        onChange={(e) => updateFormData("minInvestment", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="">Select Amount</option>
                        <option value="65K">$65K</option>
                        <option value="130K">$130K</option>
                        <option value="325K">$325K</option>
                        <option value="650K">$650K</option>
                        <option value="1.3M">$1.3M</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="maxInvestment" className="text-gray-700">Max Investment per Deal *</Label>
                      <select
                        id="maxInvestment"
                        value={formData.maxInvestment}
                        onChange={(e) => updateFormData("maxInvestment", e.target.value)}
                        className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                      >
                        <option value="">Select Amount</option>
                        <option value="650K">$650K</option>
                        <option value="1.3M">$1.3M</option>
                        <option value="2.6M">$2.6M</option>
                        <option value="6.5M">$6.5M</option>
                        <option value="13M+">$13M+</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="riskTolerance" className="text-gray-700">Risk Tolerance *</Label>
                    <select
                      id="riskTolerance"
                      value={formData.riskTolerance}
                      onChange={(e) => updateFormData("riskTolerance", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Risk Level</option>
                      <option value="conservative">Conservative (Low Risk)</option>
                      <option value="moderate">Moderate (Medium Risk)</option>
                      <option value="aggressive">Aggressive (High Risk)</option>
                      <option value="very_aggressive">Very Aggressive (Very High Risk)</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Step 3: Investment Preferences */}
              {currentStep === 3 && (
                <div className="space-y-6">
                  <div>
                    <Label className="text-gray-700 mb-3 block">Preferred Industries *</Label>
                    <MultiSelect
                      options={[...industryOptions, { value: "others", label: "Others (Open Field Entry)" }]}
                      selected={formData.preferredIndustries}
                      onChange={(selected) => updateFormData("preferredIndustries", selected)}
                      placeholder="Select preferred industries..."
                      className="bg-white border-gray-300 text-black"
                    />
                    {formData.preferredIndustries.includes("others") && (
                      <Input
                        className="mt-2 bg-white border-gray-300 text-black"
                        placeholder="Please specify other industries..."
                        onChange={(e) => updateFormData("preferredIndustriesOther", e.target.value)}
                      />
                    )}
                  </div>

                  <div>
                    <Label className="text-gray-700 mb-3 block">Preferred Investment Stages *</Label>
                    <MultiSelect
                      options={investmentStageOptions}
                      selected={formData.preferredStages}
                      onChange={(selected) => updateFormData("preferredStages", selected)}
                      placeholder="Select preferred investment stages..."
                      className="bg-white border-gray-300 text-black"
                    />
                  </div>

                  <div>
                    <Label htmlFor="investmentHorizon" className="text-gray-700">Investment Horizon</Label>
                    <select
                      id="investmentHorizon"
                      value={formData.investmentHorizon}
                      onChange={(e) => updateFormData("investmentHorizon", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Timeline</option>
                      <option value="1-2years">1-2 years</option>
                      <option value="3-5years">3-5 years</option>
                      <option value="5-7years">5-7 years</option>
                      <option value="7+years">7+ years</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="expectedReturns" className="text-gray-700">Expected Annual Returns</Label>
                    <select
                      id="expectedReturns"
                      value={formData.expectedReturns === "custom" ? "custom" : formData.expectedReturns}
                      onChange={(e) => updateFormData("expectedReturns", e.target.value)}
                      className="w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Range</option>
                      {expectedReturnsOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                    {formData.expectedReturns === "custom" && (
                      <Input
                        className="mt-2 bg-white border-gray-300 text-black"
                        placeholder="Please specify expected returns..."
                        onChange={(e) => updateFormData("expectedReturnsCustom", e.target.value)}
                      />
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-between mt-8"
        >
          <div>
            {currentStep > 1 ? (
              <Button variant="outline" onClick={prevStep} className="border-gray-300 text-black hover:bg-gray-50">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            ) : (
              <Link href="/investor">
                <Button variant="outline" className="border-gray-300 text-black hover:bg-gray-50">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Investor Page
                </Button>
              </Link>
            )}
          </div>

          <div>
            {currentStep < totalSteps ? (
              <Button onClick={nextStep} className="bg-black hover:bg-gray-800 text-white">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Link href="/investor/dashboard">
                <Button className="bg-black hover:bg-gray-800 text-white">
                  Complete Registration
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </Link>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}
