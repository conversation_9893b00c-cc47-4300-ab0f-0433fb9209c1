"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Logo from "@/components/ui/logo";
import { motion } from "framer-motion";
import { ArrowRight, Building, CheckCircle, TrendingUp, Users } from "lucide-react";
import Link from "next/link";

export default function SignUpPage() {
  const roles = [
    {
      id: "sme",
      title: "Small & Medium Enterprise",
      description: "Looking for funding and growth opportunities",
      features: [
        "Access to investors and funding opportunities",
        "Business health scoring and analytics",
        "Expert consultation and advisory services",
        "Growth acceleration programs"
      ],
      icon: Building,
      color: "blue",
      href: "/sme/signup",
      buttonText: "Start as SME"
    },
    {
      id: "investor",
      title: "Investor",
      description: "Seeking high-potential investment opportunities",
      features: [
        "Curated deal pipeline and opportunities",
        "Detailed company analytics and scoring",
        "Direct access to vetted SMEs",
        "Portfolio management tools"
      ],
      icon: TrendingUp,
      color: "emerald",
      href: "/investor/signup",
      buttonText: "Start Investing"
    },
    {
      id: "consultant",
      title: "Business Consultant",
      description: "Help SMEs grow and earn consulting fees",
      features: [
        "Connect with SMEs needing expertise",
        "Earn consulting fees and bonuses",
        "Build your professional network",
        "Access to business tools and resources"
      ],
      icon: Users,
      color: "amber",
      href: "/consultant/signup",
      buttonText: "Start Consulting"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Logo href="/" size="md" />
          <Link 
            href="/auth/signin" 
            className="text-black hover:text-gray-600 font-medium underline decoration-1 underline-offset-2 transition-colors"
          >
            Already have an account? Sign in
          </Link>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="flex justify-center mb-6">
            <Logo size="lg" />
          </div>
          <h1 className="text-4xl font-bold text-black mb-4">Choose Your Role</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Select the option that best describes you to get started with a tailored experience
          </p>
        </motion.div>

        {/* Role Selection Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {roles.map((role, index) => {
            // Consistent styling across the app - all icons use gray background
            const getIconBgClass = (color: string) => {
              return 'bg-gray-100';
            };

            const getIconColorClass = (color: string) => {
              // Ensure all icons are visible with proper contrast
              return 'text-black';
            };
            
            const getButtonClass = (color: string) => {
              // All buttons should be black for consistent app design
              return '!bg-black hover:!bg-gray-800 !text-white';
            };

            return (
              <motion.div
                key={role.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="bg-white border-gray-200 hover:border-gray-400 transition-all duration-300 h-full group hover:scale-105 shadow-lg">
                  <CardHeader className="text-center pb-4">
                    <div className={`w-16 h-16 ${getIconBgClass(role.color)} rounded-2xl flex items-center justify-center mx-auto mb-4`}>
                      <role.icon className={`w-8 h-8 ${getIconColorClass(role.color)}`} />
                    </div>
                    <CardTitle className="text-xl text-black">{role.title}</CardTitle>
                    <CardDescription className="text-gray-600">
                      {role.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-3">
                      {role.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-start space-x-3">
                          <CheckCircle className={`w-5 h-5 ${getIconColorClass(role.color)} mt-0.5 flex-shrink-0`} />
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Link href={role.href} className="block">
                      <Button className={`w-full ${getButtonClass(role.color)} font-medium`}>
                        {role.buttonText}
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
