"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import {
    Award,
    Star
} from "lucide-react";



interface Milestone {
  id: number;
  title: string;
  description: string;
  target: string;
  current: string;
  progress: number;
  status: "completed" | "in-progress" | "pending";
  reward: string;
}

export default function SME10xGrowthProgram() {

  // Mock user progress data
  const userProgress = {
    overallProgress: 45,
    completedModules: 3,
    totalModules: 8,
    currentStreak: 7,
    totalPoints: 1850,
    level: "Growth Accelerator",
    nextMilestone: "Revenue Optimization",
    programStartDate: "2024-01-15",
    estimatedCompletion: "2024-04-15"
  };



  const milestones: Milestone[] = [
    {
      id: 1,
      title: "Revenue Growth",
      description: "Achieve 30% revenue increase from baseline",
      target: "$500K",
      current: "$385K",
      progress: 77,
      status: "in-progress",
      reward: "Growth Champion Badge + $500 bonus"
    },
    {
      id: 2,
      title: "Customer Acquisition",
      description: "Reach 1000 active customers",
      target: "1000",
      current: "650",
      progress: 65,
      status: "in-progress",
      reward: "Customer Master Certificate"
    },
    {
      id: 3,
      title: "Operational Efficiency",
      description: "Reduce operational costs by 20%",
      target: "20%",
      current: "12%",
      progress: 60,
      status: "in-progress",
      reward: "Efficiency Expert Badge"
    }
  ];



  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-700 border-green-200";
      case "current": case "in-progress": return "bg-blue-100 text-blue-700 border-blue-200";
      case "locked": case "pending": return "bg-gray-100 text-gray-600 border-gray-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />
      
      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Program Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-2">
              <h1 className="text-3xl font-bold text-black">10X Growth Program</h1>
              <Badge className="bg-gradient-to-r from-yellow-100 to-orange-100 text-amber-700 border-amber-200">
                <Star className="w-4 h-4 mr-1" />
                {userProgress.level}
              </Badge>
            </div>
            <p className="text-gray-600">Transform your business with our comprehensive growth acceleration program</p>
          </div>
          <div className="text-right">
            <div className="text-h2 text-black">{userProgress.overallProgress}%</div>
            <div className="text-body-small text-gray-600">Program Complete</div>
          </div>
        </div>





        {/* Milestones Section */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-black mb-2">Program Milestones</h2>
          <p className="text-gray-600">Track your progress through the 10X Growth Program</p>
        </div>

        {/* Milestones Content */}
          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-white border-gray-200 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-black font-semibold text-lg">{milestone.title}</h3>
                        <p className="text-gray-600">{milestone.description}</p>
                      </div>
                      <Badge className={getStatusColor(milestone.status)}>
                        {milestone.status === "in-progress" ? "In Progress" : milestone.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.current}</div>
                        <div className="text-gray-600 text-sm">Current</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.progress}%</div>
                        <div className="text-gray-600 text-sm">Progress</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-black">{milestone.target}</div>
                        <div className="text-gray-600 text-sm">Target</div>
                      </div>
                    </div>
                    
                    <Progress value={milestone.progress} className="h-3 mb-4" />
                    
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <Award className="w-4 h-4 text-black" />
                        <span className="text-gray-700 text-sm font-medium">Reward:</span>
                        <span className="text-black text-sm">{milestone.reward}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
