"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    Calendar,
    CheckCircle,
    Phone,
    Star,
    User,
    Video
} from "lucide-react";
import { useState } from "react";

export default function AdvisorCallPage() {
  const { user } = useAuthStore();
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>("");
  const [callType, setCallType] = useState<"phone" | "video">("video");

  const advisors = [
    {
      id: 1,
      name: "Assigned Advisor",
      title: "Senior Business Advisor",
      expertise: ["Financial Planning", "Growth Strategy", "Investor Relations"],
      rating: 4.9,
      reviews: 127,
      experience: "15+ years",
      image: "/api/placeholder/64/64"
    }
  ];

  const timeSlots = [
    "9:00 AM", "10:00 AM", "11:00 AM", "2:00 PM", "3:00 PM", "4:00 PM"
  ];

  const upcomingCalls = [
    {
      advisor: "Assigned Advisor",
      date: "Tomorrow",
      time: "2:00 PM",
      type: "video",
      topic: "Q4 Financial Review"
    },
    {
      advisor: "Investment Specialist",
      date: "Dec 20",
      time: "10:00 AM",
      type: "phone",
      topic: "Investor Pitch Preparation"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />

      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-black mb-2">Schedule Advisor Call</h1>
              <p className="text-gray-600">Get expert guidance from our certified business advisors</p>
            </div>
            <Badge className="bg-green-100 text-green-700 border-green-200">
              Premium Feature
            </Badge>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Scheduling Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Schedule New Call
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Book a consultation with one of our expert advisors
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Advisor Selection */}
                <div>
                  <Label className="text-black mb-3 block">Select Advisor</Label>
                  <div className="space-y-3">
                    {advisors.map((advisor) => (
                      <div key={advisor.id} className="p-4 border border-gray-200 rounded-lg hover:border-gray-400 transition-colors cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                              <User className="w-6 h-6 text-black" />
                            </div>
                            <div>
                              <h3 className="text-black font-semibold">{advisor.name}</h3>
                              <p className="text-gray-600 text-sm">{advisor.title}</p>
                              <div className="flex items-center mt-1">
                                <Star className="w-4 h-4 text-amber-500 mr-1" />
                                <span className="text-gray-700 text-sm">{advisor.rating} ({advisor.reviews} reviews)</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <Badge className="bg-gray-100 text-black border-gray-200 mb-2">
                              {advisor.experience}
                            </Badge>
                            <div className="flex flex-wrap gap-1">
                              {advisor.expertise.slice(0, 2).map((skill, index) => (
                                <Badge key={index} variant="outline" className="text-xs border-gray-300 text-gray-600">
                                  {skill}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Date Selection */}
                <div>
                  <Label className="text-black mb-3 block">Select Date</Label>
                  <Input
                    type="date"
                    className="bg-white border-gray-300 text-black"
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>

                {/* Time Selection */}
                <div>
                  <Label className="text-black mb-3 block">Select Time</Label>
                  <div className="grid grid-cols-3 gap-2">
                    {timeSlots.map((time) => (
                      <Button
                        key={time}
                        variant={selectedTimeSlot === time ? "default" : "outline"}
                        className={`${
                          selectedTimeSlot === time 
                            ? "bg-black hover:bg-gray-800 text-white" 
                            : "border-gray-300 text-black hover:bg-gray-50"
                        }`}
                        onClick={() => setSelectedTimeSlot(time)}
                      >
                        {time}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Call Type */}
                <div>
                  <Label className="text-black mb-3 block">Call Type</Label>
                  <div className="flex space-x-4">
                    <Button
                      variant={callType === "video" ? "default" : "outline"}
                      className={`flex-1 ${
                        callType === "video" 
                          ? "bg-black hover:bg-gray-800 text-white" 
                          : "border-gray-300 text-black hover:bg-gray-50"
                      }`}
                      onClick={() => setCallType("video")}
                    >
                      <Video className="w-4 h-4 mr-2" />
                      Video Call
                    </Button>
                    <Button
                      variant={callType === "phone" ? "default" : "outline"}
                      className={`flex-1 ${
                        callType === "phone" 
                          ? "bg-black hover:bg-gray-800 text-white" 
                          : "border-gray-300 text-black hover:bg-gray-50"
                      }`}
                      onClick={() => setCallType("phone")}
                    >
                      <Phone className="w-4 h-4 mr-2" />
                      Phone Call
                    </Button>
                  </div>
                </div>

                {/* Topic */}
                <div>
                  <Label className="text-black mb-3 block">Discussion Topic</Label>
                  <Textarea
                    placeholder="What would you like to discuss? (e.g., fundraising strategy, financial planning, growth opportunities)"
                    className="bg-white border-gray-300 text-black placeholder-gray-500"
                    rows={3}
                  />
                </div>

                <Button className="w-full bg-black hover:bg-gray-800 text-white">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule Call
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Upcoming Calls & Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Upcoming Calls */}
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black">Upcoming Calls</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingCalls.map((call, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-black font-semibold">{call.advisor}</span>
                        {call.type === 'video' ? (
                          <Video className="w-4 h-4 text-black" />
                        ) : (
                          <Phone className="w-4 h-4 text-black" />
                        )}
                      </div>
                      <p className="text-gray-600 text-sm">{call.topic}</p>
                      <div className="flex items-center text-gray-700 text-sm mt-2">
                        <Calendar className="w-4 h-4 mr-1" />
                        {call.date} at {call.time}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black">Why Schedule a Call?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
                    <div>
                      <p className="text-black text-sm font-semibold">Expert Guidance</p>
                      <p className="text-gray-600 text-xs">Get personalized advice from certified advisors</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
                    <div>
                      <p className="text-black text-sm font-semibold">Strategic Planning</p>
                      <p className="text-gray-600 text-xs">Develop actionable growth strategies</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2 mt-0.5" />
                    <div>
                      <p className="text-black text-sm font-semibold">Investor Readiness</p>
                      <p className="text-gray-600 text-xs">Prepare for funding rounds and pitches</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
